2025-08-03 00:44:38 [按钮操作] 开始注册 -> 启动注册流程
2025-08-03 00:44:38 [信息] 开始启动多线程注册，线程数量: 3
2025-08-03 00:44:38 [信息] 开始启动多线程注册，线程数量: 3，数据条数: 6
2025-08-03 00:44:38 [信息] 所有线程已停止并清理
2025-08-03 00:44:38 [信息] 正在初始化多线程服务...
2025-08-03 00:44:38 [信息] 千川手机API服务已初始化
2025-08-03 00:44:38 [信息] 手机号码管理器已初始化，服务商: <PERSON><PERSON>chu<PERSON>，将在第一个线程完成第二页后获取手机号码
2025-08-03 00:44:38 [信息] 多线程服务初始化完成
2025-08-03 00:44:38 [信息] 数据分配完成：共6条数据分配给3个线程
2025-08-03 00:44:38 [信息] 线程1分配到2条数据
2025-08-03 00:44:38 [信息] 线程2分配到2条数据
2025-08-03 00:44:38 [信息] 线程3分配到2条数据
2025-08-03 00:44:38 [信息] 屏幕工作区域: 1280x672
2025-08-03 00:44:38 [信息] 线程1窗口布局: 位置(0, 0), 大小(384x200), 列1行1, 宽度30%, 当前列窗口数:3, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-03 00:44:38 [信息] 线程1获取到数据: Email=<EMAIL>, CountryCode=IL
2025-08-03 00:44:38 [信息] 为国家代码 IL 生成智能指纹: 时区=Asia/Jerusalem, 语言=he-IL,he;q=0.9,en;q=0.8
2025-08-03 00:44:38 [信息] 已生成高级浏览器指纹: Canvas=canvas_fp_006, WebGL=ANGLE (AMD Radeon RX 6600 Direct3D11 vs_5_0 ps_5_0), CPU=16核, RAM=4 GB
2025-08-03 00:44:38 线程1：[信息] 已创建，窗口位置: (0, 0)
2025-08-03 00:44:38 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-03 00:44:38 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-03 00:44:38 [信息] 线程1已创建，窗口位置: (0, 0)，指纹: 国家=IL, 时区=Asia/Jerusalem
2025-08-03 00:44:38 [信息] 屏幕工作区域: 1280x672
2025-08-03 00:44:38 [信息] 线程2窗口布局: 位置(0, 219), 大小(384x200), 列1行2, 宽度30%, 当前列窗口数:3, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-03 00:44:38 [信息] 线程2获取到数据: Email=<EMAIL>, CountryCode=IL
2025-08-03 00:44:38 [信息] 为国家代码 IL 生成智能指纹: 时区=Asia/Jerusalem, 语言=he-IL,he;q=0.9,en;q=0.8
2025-08-03 00:44:38 [信息] 已生成高级浏览器指纹: Canvas=canvas_fp_006, WebGL=ANGLE (NVIDIA GeForce GTX 1070 Direct3D11 vs_5_0 ps_5_0), CPU=16核, RAM=20 GB
2025-08-03 00:44:38 线程2：[信息] 已创建，窗口位置: (0, 219)
2025-08-03 00:44:38 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-03 00:44:38 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-03 00:44:38 [信息] 线程2已创建，窗口位置: (0, 219)，指纹: 国家=IL, 时区=Asia/Jerusalem
2025-08-03 00:44:38 [信息] 屏幕工作区域: 1280x672
2025-08-03 00:44:38 [信息] 线程3窗口布局: 位置(0, 438), 大小(384x200), 列1行3, 宽度30%, 当前列窗口数:3, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-03 00:44:38 [信息] 线程3获取到数据: Email=<EMAIL>, CountryCode=IL
2025-08-03 00:44:38 [信息] 为国家代码 IL 生成智能指纹: 时区=Asia/Jerusalem, 语言=he-IL,he;q=0.9,en;q=0.8
2025-08-03 00:44:38 [信息] 已生成高级浏览器指纹: Canvas=canvas_fp_007, WebGL=ANGLE (Intel(R) UHD Graphics 630 Direct3D11 vs_5_0 ps_5_0), CPU=18核, RAM=10 GB
2025-08-03 00:44:38 线程3：[信息] 已创建，窗口位置: (0, 438)
2025-08-03 00:44:38 线程3：[信息] 添加数据到队列: <EMAIL>
2025-08-03 00:44:38 线程3：[信息] 添加数据到队列: <EMAIL>
2025-08-03 00:44:38 [信息] 线程3已创建，窗口位置: (0, 438)，指纹: 国家=IL, 时区=Asia/Jerusalem
2025-08-03 00:44:38 [信息] 多线程注册启动成功，共3个线程
2025-08-03 00:44:38 线程2：[信息] 开始启动注册流程
2025-08-03 00:44:38 线程1：[信息] 开始启动注册流程
2025-08-03 00:44:38 线程3：[信息] 开始启动注册流程
2025-08-03 00:44:38 线程2：[信息] 开始启动浏览器: 位置(0, 219), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36
2025-08-03 00:44:38 线程1：[信息] 开始启动浏览器: 位置(0, 0), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0
2025-08-03 00:44:38 线程1：[信息] 启动无痕Chrome浏览器...
2025-08-03 00:44:38 线程2：[信息] 启动无痕Chrome浏览器...
2025-08-03 00:44:38 线程3：[信息] 开始启动浏览器: 位置(0, 438), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-08-03 00:44:38 线程3：[信息] 启动无痕Chrome浏览器...
2025-08-03 00:44:38 线程2：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-03 00:44:38 线程1：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-03 00:44:38 线程3：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-03 00:44:38 [信息] 多线程管理窗口已初始化
2025-08-03 00:44:38 [信息] UniformGrid列数已更新为: 1
2025-08-03 00:44:38 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 0, 列数: 1
2025-08-03 00:44:38 [信息] 多线程管理窗口已打开
2025-08-03 00:44:38 [信息] 多线程注册启动成功，共3个线程
2025-08-03 00:44:44 [信息] UniformGrid列数已更新为: 1
2025-08-03 00:44:44 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 1, 列数: 1
2025-08-03 00:44:44 线程3：[信息] [信息] 多线程模式根据指纹国家代码 IL 设置浏览器语言: עברית (进度: 0%)
2025-08-03 00:44:44 [信息] 浏览器语言设置: 多线程模式使用指纹国家代码=IL, 语言=עברית, 参数=--lang=he-IL
2025-08-03 00:44:44 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:119.0) Gecko/20100101 Firefox/119.0
2025-08-03 00:44:44 线程3：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-03 00:44:45 [信息] UniformGrid列数已更新为: 1
2025-08-03 00:44:45 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 2, 列数: 1
2025-08-03 00:44:45 线程1：[信息] [信息] 多线程模式根据指纹国家代码 IL 设置浏览器语言: עברית (进度: 0%)
2025-08-03 00:44:45 [信息] 浏览器语言设置: 多线程模式使用指纹国家代码=IL, 语言=עברית, 参数=--lang=he-IL
2025-08-03 00:44:45 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36
2025-08-03 00:44:45 线程1：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-03 00:44:45 [信息] UniformGrid列数已更新为: 2
2025-08-03 00:44:45 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 3, 列数: 2
2025-08-03 00:44:45 线程2：[信息] [信息] 多线程模式根据指纹国家代码 IL 设置浏览器语言: עברית (进度: 0%)
2025-08-03 00:44:45 [信息] 浏览器语言设置: 多线程模式使用指纹国家代码=IL, 语言=עברית, 参数=--lang=he-IL
2025-08-03 00:44:45 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36
2025-08-03 00:44:45 线程2：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-03 00:44:47 线程3：[信息] [信息] 创建无痕模式上下文... (进度: 0%)
2025-08-03 00:44:47 线程2：[信息] [信息] 创建无痕模式上下文... (进度: 0%)
2025-08-03 00:44:47 线程1：[信息] [信息] 创建无痕模式上下文... (进度: 0%)
2025-08-03 00:44:49 线程3：[信息] [信息] 多线程模式使用指纹时区: Asia/Jerusalem (进度: 0%)
2025-08-03 00:44:49 [信息] 浏览器时区设置: 多线程模式使用指纹时区=Asia/Jerusalem
2025-08-03 00:44:49 线程3：[信息] [信息] 多线程模式使用指纹语言: he-IL,he;q=0.9,en;q=0.8 (进度: 0%)
2025-08-03 00:44:49 [信息] 浏览器语言设置: 多线程模式使用指纹语言=he-IL,he;q=0.9,en;q=0.8
2025-08-03 00:44:49 线程3：[信息] [信息] 多线程模式使用指纹地理位置: 32.0853, 34.7818 (进度: 0%)
2025-08-03 00:44:49 [信息] 浏览器地理位置设置: 多线程模式使用指纹地理位置, 纬度=32.0853, 经度=34.7818
2025-08-03 00:44:49 线程2：[信息] [信息] 多线程模式使用指纹时区: Asia/Jerusalem (进度: 0%)
2025-08-03 00:44:49 [信息] 浏览器时区设置: 多线程模式使用指纹时区=Asia/Jerusalem
2025-08-03 00:44:49 线程2：[信息] [信息] 多线程模式使用指纹语言: he-IL,he;q=0.9,en;q=0.8 (进度: 0%)
2025-08-03 00:44:49 [信息] 浏览器语言设置: 多线程模式使用指纹语言=he-IL,he;q=0.9,en;q=0.8
2025-08-03 00:44:49 线程2：[信息] [信息] 多线程模式使用指纹地理位置: 32.0853, 34.7818 (进度: 0%)
2025-08-03 00:44:49 [信息] 浏览器地理位置设置: 多线程模式使用指纹地理位置, 纬度=32.0853, 经度=34.7818
2025-08-03 00:44:49 线程1：[信息] [信息] 多线程模式使用指纹时区: Asia/Jerusalem (进度: 0%)
2025-08-03 00:44:49 [信息] 浏览器时区设置: 多线程模式使用指纹时区=Asia/Jerusalem
2025-08-03 00:44:49 线程1：[信息] [信息] 多线程模式使用指纹语言: he-IL,he;q=0.9,en;q=0.8 (进度: 0%)
2025-08-03 00:44:49 [信息] 浏览器语言设置: 多线程模式使用指纹语言=he-IL,he;q=0.9,en;q=0.8
2025-08-03 00:44:49 线程1：[信息] [信息] 多线程模式使用指纹地理位置: 32.7940, 34.9896 (进度: 0%)
2025-08-03 00:44:49 [信息] 浏览器地理位置设置: 多线程模式使用指纹地理位置, 纬度=32.7940, 经度=34.9896
2025-08-03 00:44:49 线程3：[信息] [信息] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_007, CPU: 18核 (进度: 0%)
2025-08-03 00:44:49 [信息] 浏览器指纹注入: Canvas=canvas_fp_007, WebGL=ANGLE (Intel(R) UHD Graphics 630 Direct3D11 vs_5_0 ps_5_0), CPU=18核, RAM=10 GB
2025-08-03 00:44:49 线程2：[信息] [信息] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_006, CPU: 16核 (进度: 0%)
2025-08-03 00:44:49 [信息] 浏览器指纹注入: Canvas=canvas_fp_006, WebGL=ANGLE (NVIDIA GeForce GTX 1070 Direct3D11 vs_5_0 ps_5_0), CPU=16核, RAM=20 GB
2025-08-03 00:44:49 线程1：[信息] [信息] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_006, CPU: 16核 (进度: 0%)
2025-08-03 00:44:49 [信息] 浏览器指纹注入: Canvas=canvas_fp_006, WebGL=ANGLE (AMD Radeon RX 6600 Direct3D11 vs_5_0 ps_5_0), CPU=16核, RAM=4 GB
2025-08-03 00:44:51 线程1：[信息] [信息] 已创建新的无痕模式上下文和页面 (进度: 0%)
2025-08-03 00:44:51 线程3：[信息] [信息] 已创建新的无痕模式上下文和页面 (进度: 0%)
2025-08-03 00:44:51 线程2：[信息] [信息] 已创建新的无痕模式上下文和页面 (进度: 0%)
2025-08-03 00:44:53 线程3：[信息] [信息] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 18
   • 设备内存: 10 GB
   • 平台信息: Win32
   • Do Not Track: default

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (Intel(R) UHD Graphics 630 Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (NVIDIA Corp.)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 1C2D3E4F
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_002
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-E4F5G6H
   • MAC地址: 78-9A-BC-DE-F0-12
   • 屏幕分辨率: 2079x1054
   • 可用区域: 2079x1014

🌍 地区语言信息:
   • 主语言: he-IL
   • 语言列表: he-IL,en-US
   • 时区偏移: -120分钟

🔧 高级功能信息:
   • ClientRects ID: E1F2A3B4
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: 5g
   • 电池API支持: True
   • 电池电量: 0.90
   • 电池充电状态: True
   • 性能API支持: True
🔍 ================================ (进度: 5%)
2025-08-03 00:44:53 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 18    • 设备内存: 10 GB    • 平台信息: Win32    • Do Not Track: default   图形渲染信息:    • WebGL渲染器: ANGLE (Intel(R) UHD Graphics 630 Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (NVIDIA Corp.)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 1C2D3E4F    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_002    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-E4F5G6H    • MAC地址: 78-9A-BC-DE-F0-12    • 屏幕分辨率: 2079x1054    • 可用区域: 2079x1014   地区语言信息:    • 主语言: he-IL    • 语言列表: he-IL,en-US    • 时区偏移: -120分钟   高级功能信息:    • ClientRects ID: E1F2A3B4    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: 5g    • 电池API支持: True    • 电池电量: 0.90    • 电池充电状态: True    • 性能API支持: True  ================================
2025-08-03 00:44:53 线程1：[信息] [信息] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 16
   • 设备内存: 4 GB
   • 平台信息: Win32
   • Do Not Track: system

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (AMD Radeon RX 6600 Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (NVIDIA Corp.)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 0426959D
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_009
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-K8L9M0N
   • MAC地址: BC-DE-F0-12-34-56
   • 屏幕分辨率: 1749x987
   • 可用区域: 1749x947

🌍 地区语言信息:
   • 主语言: he-IL
   • 语言列表: he-IL,en-US
   • 时区偏移: -120分钟

🔧 高级功能信息:
   • ClientRects ID: C9D0E1F2
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: 2g
   • 电池API支持: True
   • 电池电量: 0.83
   • 电池充电状态: False
   • 性能API支持: True
🔍 ================================ (进度: 5%)
2025-08-03 00:44:53 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 16    • 设备内存: 4 GB    • 平台信息: Win32    • Do Not Track: system   图形渲染信息:    • WebGL渲染器: ANGLE (AMD Radeon RX 6600 Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (NVIDIA Corp.)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 0426959D    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_009    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-K8L9M0N    • MAC地址: BC-DE-F0-12-34-56    • 屏幕分辨率: 1749x987    • 可用区域: 1749x947   地区语言信息:    • 主语言: he-IL    • 语言列表: he-IL,en-US    • 时区偏移: -120分钟   高级功能信息:    • ClientRects ID: C9D0E1F2    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: 2g    • 电池API支持: True    • 电池电量: 0.83    • 电池充电状态: False    • 性能API支持: True  ================================
2025-08-03 00:44:53 线程2：[信息] [信息] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 16
   • 设备内存: 20 GB
   • 平台信息: Win32
   • Do Not Track: manual

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (NVIDIA GeForce GTX 1070 Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (Intel Corp.)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 0426959D
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_011
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-I7J8K9L
   • MAC地址: 11-22-33-44-55-66
   • 屏幕分辨率: 1761x1119
   • 可用区域: 1761x1079

🌍 地区语言信息:
   • 主语言: he-IL
   • 语言列表: he-IL,en-US
   • 时区偏移: -120分钟

🔧 高级功能信息:
   • ClientRects ID: E9F0A1B2
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: wifi
   • 电池API支持: True
   • 电池电量: 0.25
   • 电池充电状态: False
   • 性能API支持: True
🔍 ================================ (进度: 5%)
2025-08-03 00:44:53 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 16    • 设备内存: 20 GB    • 平台信息: Win32    • Do Not Track: manual   图形渲染信息:    • WebGL渲染器: ANGLE (NVIDIA GeForce GTX 1070 Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (Intel Corp.)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 0426959D    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_011    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-I7J8K9L    • MAC地址: 11-22-33-44-55-66    • 屏幕分辨率: 1761x1119    • 可用区域: 1761x1079   地区语言信息:    • 主语言: he-IL    • 语言列表: he-IL,en-US    • 时区偏移: -120分钟   高级功能信息:    • ClientRects ID: E9F0A1B2    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: wifi    • 电池API支持: True    • 电池电量: 0.25    • 电池充电状态: False    • 性能API支持: True  ================================
2025-08-03 00:44:53 线程3：[信息] [信息] 已设置浏览器标题: 线程3 - AWS注册工具 (进度: 5%)
2025-08-03 00:44:53 线程1：[信息] [信息] 已设置浏览器标题: 线程1 - AWS注册工具 (进度: 5%)
2025-08-03 00:44:53 线程3：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 5%)
2025-08-03 00:44:53 线程1：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 5%)
2025-08-03 00:44:53 线程1：[信息] 浏览器启动成功
2025-08-03 00:44:53 线程3：[信息] 浏览器启动成功
2025-08-03 00:44:53 线程1：[信息] 获取下一个数据: <EMAIL>
2025-08-03 00:44:53 线程3：[信息] 获取下一个数据: <EMAIL>
2025-08-03 00:44:54 线程1：[信息] 开始处理账户: <EMAIL>
2025-08-03 00:44:54 线程3：[信息] 开始处理账户: <EMAIL>
2025-08-03 00:44:54 线程1：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-03 00:44:54 线程1：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-03 00:44:54 线程3：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-03 00:44:54 线程1：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-03 00:44:54 线程3：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-03 00:44:54 线程1：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-03 00:44:54 线程3：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-03 00:44:54 线程1：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-03 00:44:54 线程3：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-03 00:44:54 线程3：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-03 00:44:54 线程2：[信息] [信息] 已设置浏览器标题: 线程2 - AWS注册工具 (进度: 5%)
2025-08-03 00:44:54 线程2：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 5%)
2025-08-03 00:44:54 线程2：[信息] 浏览器启动成功
2025-08-03 00:44:54 线程2：[信息] 获取下一个数据: <EMAIL>
2025-08-03 00:44:54 线程2：[信息] 开始处理账户: <EMAIL>
2025-08-03 00:44:54 线程2：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-03 00:44:54 线程2：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-03 00:44:54 线程2：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-03 00:44:54 线程2：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-03 00:44:54 线程2：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-03 00:44:54 线程1：[信息] [信息] 已设置页面视口大小为384x200 (进度: 98%)
2025-08-03 00:44:55 线程1：[信息] [信息] 已重新设置浏览器标题: 线程1 - AWS注册工具 (进度: 98%)
2025-08-03 00:44:55 线程1：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-03 00:44:55 线程3：[信息] [信息] 已设置页面视口大小为384x200 (进度: 98%)
2025-08-03 00:44:55 线程1：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-03 00:44:55 线程1：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-03 00:44:55 线程1：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-03 00:44:57 线程3：[信息] [信息] 已重新设置浏览器标题: 线程3 - AWS注册工具 (进度: 98%)
2025-08-03 00:44:57 线程3：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-03 00:44:57 线程2：[信息] [信息] 已设置页面视口大小为384x200 (进度: 98%)
2025-08-03 00:44:57 线程2：[信息] [信息] 已重新设置浏览器标题: 线程2 - AWS注册工具 (进度: 98%)
2025-08-03 00:44:57 线程2：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-03 00:44:57 线程3：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-03 00:44:57 线程3：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:119.0... (进度: 98%)
2025-08-03 00:44:57 线程3：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-03 00:44:57 线程2：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-03 00:44:57 线程2：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-03 00:44:57 线程2：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-03 00:45:18 线程2：[信息] [信息] 已设置浏览器标题并启用自动保持: 线程2 - AWS注册 (进度: 98%)
2025-08-03 00:45:18 线程2：[信息] [信息] 正在执行第一页注册... (进度: 98%)
2025-08-03 00:45:18 线程2：[信息] [信息] 🔍 等待第一页加载完成... (进度: 98%)
2025-08-03 00:45:18 线程1：[信息] [信息] 已设置浏览器标题并启用自动保持: 线程1 - AWS注册 (进度: 98%)
2025-08-03 00:45:18 线程1：[信息] [信息] 正在执行第一页注册... (进度: 98%)
2025-08-03 00:45:18 线程1：[信息] [信息] 🔍 等待第一页加载完成... (进度: 98%)
2025-08-03 00:45:18 线程2：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 98%)
2025-08-03 00:45:18 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-03 00:45:18 线程1：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 98%)
2025-08-03 00:45:18 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-03 00:45:19 线程1：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 98%)
2025-08-03 00:45:19 线程2：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 98%)
2025-08-03 00:45:20 线程3：[信息] [信息] 已设置浏览器标题并启用自动保持: 线程3 - AWS注册 (进度: 98%)
2025-08-03 00:45:20 线程3：[信息] [信息] 正在执行第一页注册... (进度: 98%)
2025-08-03 00:45:21 线程3：[信息] [信息] 🔍 等待第一页加载完成... (进度: 98%)
2025-08-03 00:45:21 线程3：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 98%)
2025-08-03 00:45:21 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-03 00:45:21 线程3：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 98%)
2025-08-03 00:45:22 线程1：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 98%)
2025-08-03 00:45:22 线程2：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 98%)
2025-08-03 00:45:22 线程1：[信息] [信息] ✅ 未检测到IP异常错误，继续流程 (进度: 100%)
2025-08-03 00:45:22 线程2：[信息] [信息] ✅ 未检测到IP异常错误，继续流程 (进度: 100%)
2025-08-03 00:45:22 线程1：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-03 00:45:22 线程2：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-03 00:45:22 线程1：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-03 00:45:22 线程2：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-03 00:45:22 线程1：[信息] [信息] 🔍 第1次检测 - Security对话框: 1个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-03 00:45:22 线程1：[信息] [信息] ✅ 第1次检测发现图形验证码！ (进度: 100%)
2025-08-03 00:45:22 线程1：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 发现验证码 (进度: 100%)
2025-08-03 00:45:22 线程1：[信息] [信息] ⚠️ 第一页检测到图形验证码，开始处理... (进度: 100%)
2025-08-03 00:45:22 线程1：[信息] [信息] 第一页图形验证码自动识别模式 (进度: 100%)
2025-08-03 00:45:22 线程1：[信息] [信息] 第一页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-03 00:45:22 线程2：[信息] [信息] 🔍 第1次检测 - Security对话框: 1个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-03 00:45:22 线程2：[信息] [信息] ✅ 第1次检测发现图形验证码！ (进度: 100%)
2025-08-03 00:45:22 线程2：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 发现验证码 (进度: 100%)
2025-08-03 00:45:22 线程2：[信息] [信息] ⚠️ 第一页检测到图形验证码，开始处理... (进度: 100%)
2025-08-03 00:45:22 线程2：[信息] [信息] 第一页图形验证码自动识别模式 (进度: 100%)
2025-08-03 00:45:22 线程2：[信息] [信息] 第一页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-03 00:45:24 线程3：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 98%)
2025-08-03 00:45:24 线程3：[信息] [信息] ✅ 未检测到IP异常错误，继续流程 (进度: 100%)
2025-08-03 00:45:24 线程3：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-03 00:45:24 线程3：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-03 00:45:24 线程3：[信息] [信息] 🔍 第1次检测 - Security对话框: 1个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-03 00:45:24 线程3：[信息] [信息] ✅ 第1次检测发现图形验证码！ (进度: 100%)
2025-08-03 00:45:24 线程3：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 发现验证码 (进度: 100%)
2025-08-03 00:45:24 线程3：[信息] [信息] ⚠️ 第一页检测到图形验证码，开始处理... (进度: 100%)
2025-08-03 00:45:24 线程3：[信息] [信息] 第一页图形验证码自动识别模式 (进度: 100%)
2025-08-03 00:45:24 线程3：[信息] [信息] 第一页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-03 00:45:26 线程2：[信息] [信息] 第一页已从iframe截取验证码图片，大小: 35518 字节 (进度: 100%)
2025-08-03 00:45:26 线程2：[信息] [信息] ✅ 图片验证通过：201x71px，35518字节，复杂度符合要求 (进度: 100%)
2025-08-03 00:45:26 线程2：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-03 00:45:26 线程1：[信息] [信息] 第一页已从iframe截取验证码图片，大小: 35114 字节 (进度: 100%)
2025-08-03 00:45:26 线程1：[信息] [信息] ✅ 图片验证通过：201x71px，35114字节，复杂度符合要求 (进度: 100%)
2025-08-03 00:45:26 线程1：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-03 00:45:28 线程1：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"4rwshn"},"taskId":"17d8d50e-6fc0-11f0-9058-7234de99a3d9"} (进度: 100%)
2025-08-03 00:45:28 线程1：[信息] [信息] 第一页第1次识别结果: 4rwshn → 转换为小写: 4rwshn (进度: 100%)
2025-08-03 00:45:28 线程1：[信息] [信息] 第一页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-03 00:45:28 线程1：[信息] [信息] 已填入验证码: 4rwshn (进度: 100%)
2025-08-03 00:45:28 线程2：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"fps43g"},"taskId":"17f52ed4-6fc0-11f0-9058-7234de99a3d9"} (进度: 100%)
2025-08-03 00:45:28 线程2：[信息] [信息] 第一页第1次识别结果: fps43g → 转换为小写: fps43g (进度: 100%)
2025-08-03 00:45:28 线程1：[信息] [信息] 已点击iframe内Submit按钮 (进度: 100%)
2025-08-03 00:45:28 线程2：[信息] [信息] 第一页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-03 00:45:28 线程3：[信息] [信息] 第一页已从iframe截取验证码图片，大小: 35251 字节 (进度: 100%)
2025-08-03 00:45:28 线程2：[信息] [信息] 已填入验证码: fps43g (进度: 100%)
2025-08-03 00:45:28 线程3：[信息] [信息] ✅ 图片验证通过：201x71px，35251字节，复杂度符合要求 (进度: 100%)
2025-08-03 00:45:28 线程3：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-03 00:45:28 线程2：[信息] [信息] 已点击iframe内Submit按钮 (进度: 100%)
